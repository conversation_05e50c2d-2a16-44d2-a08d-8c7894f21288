--cpu=Cortex-M4.fp.sp
"08_pid\startup_stm32f407xx.o"
"08_pid\main.o"
"08_pid\gpio.o"
"08_pid\dma.o"
"08_pid\i2c.o"
"08_pid\tim.o"
"08_pid\usart.o"
"08_pid\stm32f4xx_it.o"
"08_pid\stm32f4xx_hal_msp.o"
"08_pid\stm32f4xx_hal_i2c.o"
"08_pid\stm32f4xx_hal_i2c_ex.o"
"08_pid\stm32f4xx_hal_rcc.o"
"08_pid\stm32f4xx_hal_rcc_ex.o"
"08_pid\stm32f4xx_hal_flash.o"
"08_pid\stm32f4xx_hal_flash_ex.o"
"08_pid\stm32f4xx_hal_flash_ramfunc.o"
"08_pid\stm32f4xx_hal_gpio.o"
"08_pid\stm32f4xx_hal_dma_ex.o"
"08_pid\stm32f4xx_hal_dma.o"
"08_pid\stm32f4xx_hal_pwr.o"
"08_pid\stm32f4xx_hal_pwr_ex.o"
"08_pid\stm32f4xx_hal_cortex.o"
"08_pid\stm32f4xx_hal.o"
"08_pid\stm32f4xx_hal_exti.o"
"08_pid\stm32f4xx_hal_tim.o"
"08_pid\stm32f4xx_hal_tim_ex.o"
"08_pid\stm32f4xx_hal_uart.o"
"08_pid\system_stm32f4xx.o"
"08_pid\ebtn.o"
"08_pid\ringbuffer.o"
"08_pid\iic.o"
"08_pid\inv_mpu.o"
"08_pid\inv_mpu_dmp_motion_driver.o"
"08_pid\mpu6050.o"
"08_pid\oled.o"
"08_pid\oled_font.o"
"08_pid\hardware_iic.o"
"08_pid\pid.o"
"08_pid\encoder_driver.o"
"08_pid\key_driver.o"
"08_pid\led_driver.o"
"08_pid\motor_driver.o"
"08_pid\oled_driver.o"
"08_pid\uart_driver.o"
"08_pid\encoder_app.o"
"08_pid\gray_app.o"
"08_pid\key_app.o"
"08_pid\led_app.o"
"08_pid\motor_app.o"
"08_pid\mpu6050_app.o"
"08_pid\oled_app.o"
"08_pid\pid_app.o"
"08_pid\uart_app.o"
"08_pid\scheduler.o"
"08_pid\scheduler_task.o"
--strict --scatter "08_PID\08_PID.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "08_PID.map" -o 08_PID\08_PID.axf
#include "pid_app.h"

extern Encoder left_encoder;
extern Encoder right_encoder;

extern MOTOR left_motor;
extern MOTOR right_motor;

extern float yaw; // 偏航角（角度环当前值）

extern float g_line_position_error; // 循迹误差值（循迹环当前值）

/* PID 控制器实例 */
PID_T pid_speed_left;  // 左轮速度环
PID_T pid_speed_right; // 右轮速度环

PID_T pid_angle;       // 前进角度环
PID_T pid_angle_back;  // 返回角度环

PID_T pid_line;        // 循迹环

/* PID 参数定义 */
PidParams_t pid_params_left = {
    .kp = 8.0f,        
    .ki = 5.0000f,      
    .kd = 0.00f,      
    .out_min = -999.0f,
    .out_max = 999.0f,
};

PidParams_t pid_params_right = {
    .kp = 8.0f,        
    .ki = 5.0000f,      
    .kd = 0.00f,      
    .out_min = -999.0f,
    .out_max = 999.0f,
};

PidParams_t pid_params_angle = {
    .kp = 20.0f,        
    .ki = 1.0000f,      
    .kd = 0.00f,      
    .out_min = -999.0f,
    .out_max = 999.0f,
};

PidParams_t pid_params_angle_back = {
    .kp = 140.0f * 0.6f,        
    .ki = 0.05000f,      
    .kd = 100.00f,      
    .out_min = -999.0f,
    .out_max = 999.0f,
};

PidParams_t pid_params_line = {
    .kp = 140.0f,        
    .ki = 0.0000f,      
    .kd = 0.00f,      
    .out_min = -999.0f,
    .out_max = 999.0f,
};

void PID_Init(void)
{
  pid_init(&pid_speed_left,
           pid_params_left.kp, pid_params_left.ki, pid_params_left.kd,
           0.0f, pid_params_left.out_max);
  
  pid_init(&pid_speed_right,
           pid_params_right.kp, pid_params_right.ki, pid_params_right.kd,
           0.0f, pid_params_right.out_max);
  
  pid_init(&pid_angle,
           pid_params_angle.kp, pid_params_angle.ki, pid_params_angle.kd,
           0.0f, pid_params_angle.out_max);
  
  pid_init(&pid_line,
           pid_params_line.kp, pid_params_line.ki, pid_params_line.kd,
           0.0f, pid_params_line.out_max);
  
  pid_init(&pid_angle_back,
           pid_params_angle_back.kp, pid_params_angle_back.ki, pid_params_angle_back.kd,
           0.0f, pid_params_angle_back.out_max);
  
  pid_set_target(&pid_speed_left, 0);
  pid_set_target(&pid_speed_right, 0);
  pid_set_target(&pid_angle, 0);
  pid_set_target(&pid_angle_back, 0);
  pid_set_target(&pid_line, 0);
}

bool pid_running = false; // PID 控制使能开关

bool pid_mode = 0; // 0-角度环控制，1-循迹环控制

int output_left = 0, output_right = 0, angle_output = 0, line_output = 0;
bool angle_mode = 0; // 0-前进，1-返回

extern unsigned char yaw_mode;

void PID_Task(void)
{
  if(pid_running == false) return;
  

    // 使用增量式 PID 计算利用速度环计算输出
    output_left = pid_calculate_incremental(&pid_speed_left, left_encoder.speed_cm_s);
    output_right = pid_calculate_incremental(&pid_speed_right, right_encoder.speed_cm_s);
  
    if(pid_mode == 0)  // 角度环控制
    {
      // 使用位置式 PID 计算利用角度环计算输出
      if(angle_mode == 0) // 前进
        angle_output = pid_calculate_positional(&pid_angle, yaw);
      else // 返回
        angle_output = pid_calculate_positional(&pid_angle_back, yaw);
      
      // 差速转弯
      output_left = output_left - angle_output;
      output_right = output_right + angle_output;
    }
    else   // 循迹环控制
    {
      // 使用位置式 PID 计算利用循迹环计算输出
      line_output = pid_calculate_positional(&pid_line, g_line_position_error);
      
      // 差速转弯
      output_left = output_left - line_output;
      output_right = output_right + line_output;
    }
  
    // 输出限幅
    output_left = pid_constrain(output_left, pid_params_left.out_min, pid_params_left.out_max);
    output_right = pid_constrain(output_right, pid_params_right.out_min, pid_params_right.out_max);
    
    // 设置电机速度
    Motor_Set_Speed(&left_motor, output_left);
    Motor_Set_Speed(&right_motor, output_right);
}

